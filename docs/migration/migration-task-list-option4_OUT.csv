﻿Task ID,Section,Subsection,Task Name,Owner,Status,Priority,Estimated Hours,Jon Hours,Dependencies,Start Date,End Date,Notes,Deliverables,Time Notes,Automate?
DISC-001,Discovery & Planning,Analysis,Analyze Current CakePHP Codebase,Jon,Not Started,Critical,,0,,2025-09-29,2025-09-30,Document all controllers models views and components,CakePHP functionality audit document,12,10%
DISC-002,Discovery & Planning,Analysis,Database Schema Analysis,Jon,Not Started,Critical,,0,DISC-001,2025-09-30,2025-09-30,Map all tables relationships and data structures,Database schema mapping document,6,25%
DISC-003,Discovery & Planning,Analysis,Third-Party Integration Audit,Jon,Not Started,High,,0,DISC-001,2025-09-30,2025-09-30,Document Feefo Google APIs CRM integrations,Integration requirements document,3,15%
DISC-004,Discovery & Planning,Analysis,Current AWS Infrastructure Audit,Ashley,Not Started,High,,=,,2025-09-29,2025-09-30,Document existing EB RDS S3 CloudFront setup,Infrastructure inventory document,6,20%
DISC-005,Discovery & Planning,Planning,WordPress Architecture Design,Jon,Not Started,Critical,8,8,DISC-002,2025-10-01,2025-10-01,Design custom post types ACF fields and relationships,WordPress architecture specification,,5%
AWS-001,AWS Infrastructure,Setup,Create RDS MySQL 8.0 Instance,Ashley,Not Started,Critical,3,=,DISC-004,2025-10-02,2025-10-02,Set up production database with Multi-AZ,RDS instance configured,,80%
AWS-002,AWS Infrastructure,Setup,Configure S3 Media Storage,Ashley,Not Started,High,2,=,AWS-001,2025-10-02,2025-10-02,Set up media bucket with proper permissions,S3 media storage configured,,85%
AWS-003,AWS Infrastructure,Setup,Set Up Elastic Beanstalk PHP 8.1,Ashley,Not Started,Critical,4,=,AWS-002,2025-10-03,2025-10-03,Configure PHP 8.1 environment with extensions,Elastic Beanstalk environment ready,,75%
AWS-004,AWS Infrastructure,Setup,Configure CloudFront CDN,Ashley,Not Started,Medium,2,"=IF(OR(E10=""Jon"",E10=""Both""),H10,"""")",AWS-003,2025-10-03,2025-10-03,Set up CDN for static assets,CloudFront distribution configured,,90%
AWS-005,AWS Infrastructure,Setup,Set Up SSL Certificate,Ashley,Not Started,High,2,=,AWS-004,2025-10-04,2025-10-04,Configure SSL certificate and domain routing,SSL certificate configured,,95%
AWS-006,AWS Infrastructure,Setup,Configure Monitoring and Alerts,Ashley,Not Started,Medium,3,=,AWS-005,2025-10-04,2025-10-04,Set up CloudWatch monitoring,Monitoring configured,,70%
AWS-007,AWS Infrastructure,Setup,Test Staging Environment,Ashley,Not Started,High,3,=,AWS-006,2025-10-07,2025-10-07,Validate staging environment functionality,Staging environment validated,,30%
WP-001,WordPress Development,Setup,Install WordPress Core,Jon,Not Started,Critical,2,2,DISC-005,2025-10-06,2025-10-06,Install WordPress with proper configuration,WordPress installed,,90%
WP-002,WordPress Development,Setup,Install Required Plugins,Jon,Not Started,High,3,3,WP-001,2025-10-06,2025-10-06,"Install ACF Pro, Gravity Forms, WP Rocket, Yoast SEO",Core plugins installed,,85%
WP-003,WordPress Development,Setup,Configure Plugin Licenses,Jon,Not Started,High,1,1,WP-002,2025-10-07,2025-10-07,Activate all plugin licenses,Plugin licenses activated,,50%
WP-004,WordPress Development,Custom Post Types,Register Destination Post Type,Jon,Not Started,Critical,3,3,WP-003,2025-10-07,2025-10-07,Create destination post type with hierarchy,Destination post type registered,,70%
WP-005,WordPress Development,Custom Post Types,Register Accommodation Post Type,Jon,Not Started,High,2,2,WP-004,2025-10-08,2025-10-08,Create accommodation post type,Accommodation post type registered,,75%
WP-006,WordPress Development,Custom Post Types,Register Activity Post Type,Jon,Not Started,High,2,2,WP-005,2025-10-08,2025-10-08,Create activity post type,Activity post type registered,,75%
WP-007,WordPress Development,Custom Post Types,Register Holiday Type Post Type,Jon,Not Started,Medium,2,2,WP-006,2025-10-09,2025-10-09,Create holiday type taxonomy,Holiday type post type registered,,75%
WP-008,WordPress Development,Custom Post Types,Register Itinerary Post Type,Jon,Not Started,High,3,3,WP-007,2025-10-09,2025-10-09,Create itinerary post type with day structure,Itinerary post type registered,,70%
WP-009,WordPress Development,ACF Configuration,Create Destination Field Groups,Jon,Not Started,Critical,4,4,WP-004,2025-10-10,2025-10-10,Set up destination custom fields,Destination ACF fields configured,,60%
WP-010,WordPress Development,ACF Configuration,Create Accommodation Field Groups,Jon,Not Started,High,3,3,WP-005,2025-10-10,2025-10-10,Set up accommodation custom fields,Accommodation ACF fields configured,,60%
WP-011,WordPress Development,ACF Configuration,Create Activity Field Groups,Jon,Not Started,High,2,2,WP-006,2025-10-11,2025-10-11,Set up activity custom fields,Activity ACF fields configured,,65%
WP-012,WordPress Development,ACF Configuration,Create Itinerary Field Groups,Jon,Not Started,High,3,3,WP-008,2025-10-11,2025-10-11,Set up itinerary and day custom fields,Itinerary ACF fields configured,,60%
WP-013,WordPress Development,Theme,Develop Base Theme,Jon,Not Started,Critical,12,12,WP-012,2025-10-14,2025-10-16,Create responsive WordPress theme,Base theme developed,,20%
WP-014,WordPress Development,Theme,Create Template Files,Jon,Not Started,Critical,8,8,WP-013,2025-10-16,2025-10-17,Create all required template files,Template files created,,25%
WP-015,WordPress Development,Design,Widen Template Layouts,Jon,Not Started,High,6,6,WP-014,2025-10-17,2025-10-17,Increase template widths for better content display,Template layouts widened,,40%
WP-016,WordPress Development,Design,Minor Layout Amendments,Jon,Not Started,Medium,4,4,WP-015,2025-10-18,2025-10-18,Make minor layout improvements to various templates,Layout amendments completed,,30%
WP-017,WordPress Development,Theme,Implement Navigation System,Jon,Not Started,High,8,8,WP-016,2025-10-21,2025-10-21,Create advanced navigation menus and mobile menu with WordPress menu management,Navigation system implemented,,35%
WP-018,WordPress Development,Performance,Configure WP Rocket,Jon,Not Started,Medium,2,2,WP-017,2025-10-21,2025-10-21,Set up caching and performance optimization,WP Rocket configured,,80%
WP-019,WordPress Development,SEO,Configure Yoast SEO,Jon,Not Started,High,2,2,WP-018,2025-10-22,2025-10-22,Set up SEO optimization and XML sitemaps,Yoast SEO configured,,75%
WP-020,WordPress Development,SEO,Set Up URL Redirects,Jon,Not Started,Critical,4,4,WP-019,2025-10-22,2025-10-22,Create 301 redirects from old CakePHP URLs to new WordPress URLs,URL redirects implemented,,85%
WP-021,WordPress Development,SEO,Migrate SEO Meta Data,Jon,Not Started,High,3,3,WP-020,2025-10-23,2025-10-23,Transfer meta descriptions from ACF to Yoast SEO,SEO meta data migrated,,90%
WP-022,WordPress Development,Security,Configure Security Settings,Jon,Not Started,High,2,2,WP-021,2025-10-23,2025-10-23,Set up security configurations and user permissions,Security settings configured,,70%
FORMS-001,Forms Development,Setup,Create Travel Plans Form,Jon,Not Started,Critical,6,6,WP-022,2025-10-24,2025-10-24,Recreate travel planning form in Gravity Forms,Travel plans form created,,40%
FORMS-002,Forms Development,Setup,Create Contact Forms,Jon,Not Started,High,4,4,FORMS-001,2025-10-24,2025-10-24,Create contact and enquiry forms,Contact forms created,,50%
FORMS-003,Forms Development,Integration,Configure Form Notifications,Jon,Not Started,High,3,3,FORMS-002,2025-10-25,2025-10-25,Set up email notifications and confirmations,Form notifications configured,,60%
FORMS-004,Forms Development,Integration,Set Up ReCaptcha Integration,Jon,Not Started,Medium,2,2,FORMS-003,2025-10-25,2025-10-25,Implement spam protection,ReCaptcha integrated,,80%
MIGRATE-001,Data Migration,Preparation,Create Migration Scripts,Jon,Not Started,Critical,20,20,DISC-002,2025-11-10,2025-11-13,Develop automated migration scripts with validation,Migration scripts created,,85%
MIGRATE-002,Data Migration,Preparation,Set Up Migration Environment,Jon,Not Started,High,3,3,MIGRATE-001,2025-11-13,2025-11-13,Prepare staging environment for migration,Migration environment ready,,70%
MIGRATE-003,Data Migration,Users,Migrate User Accounts,Jon,Not Started,High,1,1,MIGRATE-002,2025-11-14,2025-11-14,Transfer admin users to WordPress,User accounts migrated,3,95%
MIGRATE-004,Data Migration,Images,Migrate Image Files,Jon,Not Started,Critical,6,6,MIGRATE-003,2025-11-14,2025-11-17,Transfer all images to WordPress media library,Images migrated,,90%
MIGRATE-005,Data Migration,Content,Migrate Destinations,Jon,Not Started,Critical,8,8,MIGRATE-004,2025-11-17,2025-11-18,Transfer destination data with hierarchy,Destinations migrated,,85%
MIGRATE-006,Data Migration,Content,Migrate Accommodation,Jon,Not Started,Critical,6,6,MIGRATE-005,2025-11-18,2025-11-19,Transfer accommodation data and relationships,Accommodation migrated,CHECK,90%
MIGRATE-007,Data Migration,Content,Migrate Activities,Jon,Not Started,High,4,4,MIGRATE-006,2025-11-19,2025-11-19,Transfer activity data,Activities migrated,,95%
MIGRATE-008,Data Migration,Content,Migrate Holiday Types,Jon,Not Started,High,3,3,MIGRATE-007,2025-11-20,2025-11-20,Transfer holiday type categories,Holiday types migrated,,95%
MIGRATE-009,Data Migration,Content,Migrate Itineraries,Jon,Not Started,High,6,6,MIGRATE-008,2025-11-20,2025-11-21,Transfer itinerary data with day structure,Itineraries migrated,,80%
MIGRATE-010,Data Migration,Content,Migrate CMS Pages,Jon,Not Started,High,4,4,MIGRATE-009,2025-11-21,2025-11-21,Transfer static page content,CMS pages migrated,,85%
MIGRATE-011,Data Migration,Content,Migrate Spotlights,Jon,Not Started,High,4,4,MIGRATE-010,2025-11-24,2025-11-24,Transfer spotlight/featured content data,Spotlights migrated,,90%
MIGRATE-012,Data Migration,Content,Migrate Content Blocks,Jon,Not Started,Critical,8,8,MIGRATE-011,2025-11-24,2025-11-25,Transfer flexible content blocks,Content blocks migrated,,75%
MIGRATE-013,Data Migration,Relationships,Set Up Content Relationships,Jon,Not Started,Critical,6,6,MIGRATE-012,2025-11-25,2025-11-26,Configure all many-to-many relationships,Relationships configured,,70%
MIGRATE-014,Data Migration,Security,Migrate User Roles and Permissions,Jon,Not Started,High,4,4,MIGRATE-003,2025-11-14,2025-11-14,Convert CakePHP ACL system to WordPress roles,User permissions migrated,,80%
MIGRATE-015,Data Migration,Forms,Migrate Travel Plan Submissions,Jon,Not Started,Medium,,0,FORMS-001,2025-11-26,2025-11-26,Transfer existing form submissions,Form data migrated,3,85%
MIGRATE-016,Data Migration,Validation,Validate Migration Data,Jon,Not Started,Critical,6,6,MIGRATE-013,2025-11-26,2025-11-27,Verify all data migrated correctly,Migration validated,,60%
MIGRATE-017,Data Migration,Optimization,Optimize WordPress Database,Jon,Not Started,Medium,,0,MIGRATE-016,2025-11-27,2025-11-27,Optimize WordPress database after migration,Database optimized,2,90%
INTEGRATE-001,Third-Party Integration,Feefo,Implement Feefo Reviews Integration,Jon,Not Started,High,4,4,WP-022,2025-11-27,2025-11-27,Connect to Feefo API for customer reviews,Feefo integration implemented,,50%
INTEGRATE-002,Third-Party Integration,Google,Implement Google APIs Integration,Jon,Not Started,Medium,3,3,INTEGRATE-001,2025-11-28,2025-11-28,Set up Google Maps and other services,Google APIs integrated,,60%
BLOG-001,Blog Migration,Analysis,Analyze Current Blog Structure,Jon,Not Started,High,,0,DISC-001,2025-11-28,2025-11-28,Document Bedrock/Timber implementation,Blog analysis completed,3,20%
BLOG-002,Blog Migration,Conversion,Convert Twig Templates to PHP,Jon,Not Started,High,6,6,BLOG-001,2025-12-01,2025-12-02,Convert all Timber templates to standard WordPress,Blog templates converted,8,30%
BLOG-003,Blog Migration,Content,Migrate Blog Content,Jon,Not Started,High,8,8,BLOG-002,2025-12-02,2025-12-03,Transfer all blog posts and pages,Blog content migrated,4,85%
BLOG-004,Blog Migration,Integration,Integrate Blog with Main Site,Jon,Not Started,High,,0,BLOG-003,2025-12-03,2025-12-03,Ensure seamless navigation between blog and main site,Blog integration completed,3,40%
TEST-001,Testing,Technical Testing,Execute Technical Testing Phase,Jon,Not Started,Critical,32,32,MIGRATE-014,2026-01-08,2026-01-13,"Execute all technical tests: infrastructure, database, WordPress, content, forms, integrations",Technical testing completed,,40%
TEST-002,Testing,Security & Performance,Execute Security and Performance Testing,Jon,Not Started,High,12,12,TEST-001,2026-01-13,2026-01-15,"Execute security and performance tests: load testing, security validation",Security and performance testing completed,Jon,50%
TEST-003,Testing,User Acceptance,Conduct Staff Testing and Sign-off,Staff,Not Started,Critical,32,=,TEST-002,2026-01-15,2026-01-21,"Execute user acceptance testing: staff training, content review, functionality testing",Staff testing and sign-off completed,,5%
DEPLOY-001,Deployment,Staging,Deploy to Staging Environment,Jon,Not Started,High,3,3,TEST-003,2026-01-21,2026-01-22,Deploy complete solution to staging,Staging deployment completed,,80%
DEPLOY-002,Deployment,Validation,Validate Staging Environment,Jon,Not Started,High,6,6,DEPLOY-001,2026-01-22,2026-01-23,Comprehensive testing on staging,Staging validation completed,,30%
DEPLOY-003,Deployment,Production,Deploy to Production,Jon,Not Started,Critical,4,4,DEPLOY-002,2026-01-23,2026-01-24,Execute production deployment,Production deployment completed,,75%
DEPLOY-004,Deployment,DNS,Execute DNS Cutover,Jon,Not Started,Critical,2,2,DEPLOY-003,2026-01-24,2026-01-24,Switch DNS to new environment,DNS cutover completed,,90%
DEPLOY-005,Deployment,Verification,Post-Deployment Verification,Both,Not Started,Critical,3,3,DEPLOY-004,2026-01-24,2026-01-27,Verify production functionality,Post-deployment verification completed,,25%
TRAIN-001,Training & Handover,Training,Conduct Staff Training,Jon,Not Started,High,6,6,DEPLOY-005,2026-01-27,2026-01-28,Train staff on WordPress administration,Staff training completed,,10%
TRAIN-002,Training & Handover,Documentation,Create User Documentation,Jon,Not Started,Medium,6,6,TRAIN-001,2026-01-28,2026-01-29,Create comprehensive user guides,User documentation completed,,30%
TRAIN-003,Training & Handover,Support,Provide Go-Live Support,Both,Not Started,High,12,12,DEPLOY-005,2026-01-24,2026-01-30,Monitor and support during initial go-live period,Go-live support provided,,15%
CLOSE-001,Project Closure,Review,Conduct Project Review,Both,Not Started,Medium,3,3,TRAIN-002,2026-01-29,2026-01-30,Review project outcomes and lessons learned,Project review completed,,20%
CLOSE-002,Project Closure,Sign-off,Obtain Final Sign-off,Client,Not Started,Critical,1,=,CLOSE-001,2026-01-30,2026-01-30,Get formal project completion approval,Final sign-off obtained,,0%
,,,,,,,,,,,,,,,
,,,,,,,355,303
