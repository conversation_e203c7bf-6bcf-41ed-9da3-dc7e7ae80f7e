Task ID,Section,Subsection,Task Name,Owner,Status,Priority,Estimated Hours,Jon Hours,Dependencies,Start Date,End Date,Notes,Deliverables,Time Notes,Automate?
PHASE1-001,Phase 1: Foundation,Discovery & Planning,Project Kickoff & Stakeholder Alignment,<PERSON>,Not Started,Critical,7.5,7.5,,09-22-25,09-22-25,Define success criteria and acceptance criteria,Project charter and communication protocols,Discovery & Planning: 4 days = 30 hrs,5.0%
PHASE1-002,Phase 1: Foundation,Discovery & Planning,Local Development Environment Setup,Jon,Not Started,Critical,3.75,3.75,PHASE1-001,09-22-25,09-23-25,Configure local development environment,Development environment ready,,80.0%
PHASE1-003,Phase 1: Foundation,Discovery & Planning,Content Audit & Migration Planning,Jon,Not Started,Critical,11.25,11.25,PHASE1-002,09-23-25,09-25-25,"Complete inventory of existing content types (2,847 destinations, 1,205 accommodations, 16,182 images)",Content inventory and migration plan,,25.0%
PHASE1-004,Phase 1: Foundation,Discovery & Planning,Technical Architecture Planning,<PERSON>,Not Started,Critical,3.75,3.75,PHASE1-003,09-25-25,09-26-25,Plan WordPress custom post type structure and automation strategy,WordPress architecture specification,,15.0%
PHASE1-005,Phase 1: Foundation,Discovery & Planning,URL Structure Planning,Jon,Not Started,Critical,3.75,3.75,PHASE1-004,09-29-25,09-30-25,"Plan campaign page redirects, Route 66 destination, What's Hot → Inspiration",URL structure and redirect mapping,,30.0%
AWS-001,Phase 1: Foundation,AWS Infrastructure,AWS Account & Security Setup,Ashley,Not Started,Critical,8,0,PHASE1-001,09-22-25,09-23-25,Configure AWS account with proper IAM roles,AWS security configuration,,70.0%
AWS-002,Phase 1: Foundation,AWS Infrastructure,RDS Database Setup,Ashley,Not Started,Critical,12,0,AWS-001,09-24-25,09-25-25,Provision RDS MySQL instance with proper sizing,RDS instance configured,,80.0%
AWS-003,Phase 1: Foundation,AWS Infrastructure,S3 Media Storage Configuration,Ashley,Not Started,High,8,0,AWS-002,09-26-25,09-26-25,Create S3 buckets for media storage,S3 media storage configured,,85.0%
AWS-004,Phase 1: Foundation,AWS Infrastructure,Elastic Beanstalk Environment,Ashley,Not Started,Critical,16,0,AWS-003,09-29-25,10-01-25,Create and configure EB application,Elastic Beanstalk environment ready,,75.0%
AWS-005,Phase 1: Foundation,AWS Infrastructure,Domain & SSL Configuration,Ashley,Not Started,High,8,0,AWS-004,10-02-25,10-02-25,Configure DNS settings and SSL certificates,SSL certificate configured,,90.0%
AWS-006,Phase 1: Foundation,AWS Infrastructure,Monitoring & Logging Setup,Ashley,Not Started,Medium,12,0,AWS-005,10-03-25,10-06-25,Configure CloudWatch monitoring and alerting,Monitoring configured,,70.0%
AWS-007,Phase 1: Foundation,AWS Infrastructure,Infrastructure Testing & Validation,Ashley,Not Started,High,15,0,AWS-006,10-07-25,10-09-25,Test all AWS services and integrations,Infrastructure validated,,30.0%
WP-001,Phase 1: Foundation,WordPress Development,WordPress Core Installation,Jon,Not Started,Critical,7.5,7.5,PHASE1-005,10-01-25,10-01-25,Install WordPress on AWS Elastic Beanstalk,WordPress installed,WordPress Development: 14-16 days = 105-120 hrs,90.0%
WP-002,Phase 1: Foundation,WordPress Development,Essential Plugin Integration,Jon,Not Started,High,11.25,11.25,WP-001,10-02-25,10-03-25,"Install and configure required plugins (ACF Pro, Yoast SEO, etc.)",Core plugins installed,Total Plugin Budget: £601/year,60.0%
WP-003,Phase 1: Foundation,WordPress Development,Custom Post Types Development,Jon,Not Started,Critical,15,15,WP-002,10-06-25,10-08-25,Create Destinations Accommodation Activities custom post types,Custom post types registered,,70.0%
WP-004,Phase 1: Foundation,WordPress Development,ACF Field Groups Configuration,Jon,Not Started,Critical,12,12,WP-003,10-09-25,10-10-25,Design and implement field groups for all post types,ACF field groups configured,,60.0%
WP-005,Phase 1: Foundation,WordPress Development,Theme Development Foundation,Jon,Not Started,Critical,15,15,WP-004,10-13-25,10-15-25,Create custom theme based on design requirements,Base theme developed,,25.0%
WP-006,Phase 1: Foundation,WordPress Development,Template Architecture,Jon,Not Started,Critical,9,9,WP-005,10-16-25,10-17-25,"Base template development (single column, full width, off-white background)",Template architecture complete,,40.0%
PHASE2-001,Phase 2: Development,WordPress Development,Single Column View Implementation,Jon,Not Started,High,9,9,WP-006,10-20-25,10-21-25,Remove left/right sidebars and testimonials,Single column template implemented,,30.0%
PHASE2-002,Phase 2: Development,WordPress Development,Full Width Template,Jon,Not Started,High,6,6,PHASE2-001,10-22-25,10-22-25,Widen template to full width (1200px limit),Full width template implemented,,40.0%
PHASE2-003,Phase 2: Development,WordPress Development,Hero Image Size Adjustments,Jon,Not Started,Medium,3,3,PHASE2-002,10-23-25,10-23-25,Increase hero image size and mobile optimization,Hero image improvements complete,,35.0%
PHASE2-004,Phase 2: Development,WordPress Development,Mobile Image Positioning,Jon,Not Started,Medium,3,3,PHASE2-003,10-24-25,10-24-25,Move first image below overview tab on mobile,Mobile positioning implemented,,40.0%
PHASE2-005,Phase 2: Development,WordPress Development,Landing Page Text Structure,Jon,Not Started,Medium,6,6,PHASE2-004,10-27-25,10-28-25,Break up chunky text for better readability,Text structure improvements,,20.0%
PHASE2-006,Phase 2: Development,WordPress Development,Video Removal from Places to Stay,Jon,Not Started,Low,1.5,1.5,PHASE2-005,10-29-25,10-29-25,Remove generic video from Places to Stay pages,Video removal complete,,80.0%
PHASE2-007,Phase 2: Development,WordPress Development,Talk to Expert Section,Jon,Not Started,High,9,9,PHASE2-006,10-30-25,11-03-25,Design and implement expert consultation section,Expert section implemented,,25.0%
PHASE2-008,Phase 2: Development,WordPress Development,Call to Action Implementation,Jon,Not Started,High,10.5,10.5,PHASE2-007,11-04-25,11-06-25,Implement Audley Travel approach - sticky mobile CTA,CTA system implemented,,35.0%
PHASE2-009,Phase 2: Development,WordPress Development,Make an Enquiry Button,Jon,Not Started,High,4.5,4.5,PHASE2-008,11-07-25,11-07-25,Add enquiry button to expert section,Enquiry button implemented,,50.0%
PHASE2-010,Phase 2: Development,WordPress Development,Sales Team Contact Box,Jon,Not Started,Medium,6,6,PHASE2-009,11-10-25,11-11-25,Create sales team contact box for page bottoms,Sales team box implemented,,40.0%
PHASE2-011,Phase 2: Development,WordPress Development,Travel Plans Form Creation,Jon,Not Started,Critical,9,9,PHASE2-010,11-12-25,11-13-25,Recreate travel planning form in Gravity Forms,Travel plans form created,,40.0%
PHASE2-012,Phase 2: Development,WordPress Development,Contact Forms Creation,Jon,Not Started,High,6,6,PHASE2-011,11-14-25,11-14-25,Create contact and enquiry forms,Contact forms created,,50.0%
MIGRATE-001,Phase 2: Development,Migration Preparation,Migration Scripts Development,Jon,Not Started,Critical,24,24,PHASE2-012,11-17-25,11-19-25,Develop automated migration scripts with validation,Migration scripts created,,85.0%
MIGRATE-002,Phase 2: Development,Migration Preparation,Migration Environment Setup,Jon,Not Started,High,6,6,MIGRATE-001,11-20-25,11-20-25,Prepare staging environment for migration,Migration environment ready,,70.0%
MIGRATE-003,Phase 2: Development,SEOTravel Requirements,Campaign Page Redirects,Jon,Not Started,High,3,3,MIGRATE-002,11-21-25,11-21-25,Backup content and create redirects to homepage,Campaign redirects implemented,SEOTravel Requirements: 3-4 days = 22.5-30 hrs,85.0%
MIGRATE-004,Phase 2: Development,SEOTravel Requirements,Route 66 Destination Creation,Jon,Not Started,High,6,6,MIGRATE-003,11-24-25,11-25-25,Create new Route 66 destination page with redirects,Route 66 destination created,,60.0%
MIGRATE-005,Phase 2: Development,SEOTravel Requirements,Activity URL Structure Fix,Jon,Not Started,Medium,4.5,4.5,MIGRATE-004,11-26-25,11-26-25,Resolve activity vs activities URL structure,Activity URLs standardized,,70.0%
MIGRATE-006,Phase 2: Development,SEOTravel Requirements,What's Hot to Inspiration,Jon,Not Started,Medium,3,3,MIGRATE-005,11-27-25,11-27-25,Keep pages but implement redirects to Inspiration,Inspiration redirects implemented,,80.0%
MIGRATE-007,Phase 2: Development,Content Migration,User Accounts Migration,Jon,Not Started,High,3,3,MIGRATE-006,11-28-25,11-28-25,Transfer admin users to WordPress,User accounts migrated,Content Migration: 6-7 days = 45-52.5 hrs,95.0%
MIGRATE-008,Phase 2: Development,Content Migration,Image Files Migration,Jon,Not Started,Critical,9,9,MIGRATE-007,12-01-25,12-02-25,"Transfer all images to WordPress media library (16,182 images)",Images migrated,,90.0%
MIGRATE-009,Phase 2: Development,Content Migration,Destinations Migration,Jon,Not Started,Critical,12,12,MIGRATE-008,12-03-25,12-05-25,"Transfer destination data with hierarchy (2,847 destinations)",Destinations migrated,,85.0%
MIGRATE-010,Phase 2: Development,Content Migration,Accommodation Migration,Jon,Not Started,Critical,9,9,MIGRATE-009,12-08-25,12-09-25,"Transfer accommodation data and relationships (1,205 accommodations)",Accommodation migrated,,90.0%
MIGRATE-011,Phase 2: Development,Content Migration,Activities Migration,Jon,Not Started,High,6,6,MIGRATE-010,12-10-25,12-10-25,Transfer activity data,Activities migrated,,95.0%
MIGRATE-012,Phase 2: Development,Content Migration,Content Relationships Setup,Jon,Not Started,Critical,9,9,MIGRATE-011,12-11-25,12-12-25,Configure all many-to-many relationships,Relationships configured,,70.0%
MIGRATE-013,Phase 2: Development,Content Migration,Migration Validation,Jon,Not Started,Critical,6,6,MIGRATE-012,12-13-25,12-13-25,Verify all data migrated correctly (99%+ accuracy target),Migration validated,,60.0%
HOLIDAY-001,Phase 3: Holiday Break,Break Period,Christmas Break,All,Not Started,N/A,0,0,,12-20-25,01-06-26,Complete development break - no work scheduled,Team rest period,,0.0%
PHASE4-001,Phase 4: Launch,Testing & Bug Fixes,Content Integration & Testing,Jon,Not Started,Critical,9,9,MIGRATE-013,01-07-26,01-08-26,Establish content relationships and navigation,Content integration complete,Testing & Bug Fixes: 4-5 days = 30-37.5 hrs,40.0%
PHASE4-002,Phase 4: Launch,Testing & Bug Fixes,Search Functionality Implementation,Jon,Not Started,High,6,6,PHASE4-001,01-09-26,01-09-26,Configure SearchWP for advanced search capabilities,Search functionality implemented,,60.0%
PHASE4-003,Phase 4: Launch,Testing & Bug Fixes,Automated Testing Suite Deployment,Jon,Not Started,Critical,6,6,PHASE4-002,01-10-26,01-10-26,Deploy comprehensive testing suite (95%+ coverage target),Testing suite deployed,,80.0%
PHASE4-004,Phase 4: Launch,Testing & Bug Fixes,Performance Optimization,Jon,Not Started,High,6,6,PHASE4-003,01-13-26,01-13-26,Optimize database queries and caching,Performance optimized,,70.0%
PHASE4-005,Phase 4: Launch,Testing & Bug Fixes,SEO & Analytics Setup,Jon,Not Started,High,4.5,4.5,PHASE4-004,01-14-26,01-14-26,Configure Yoast SEO and Google Analytics,SEO configuration complete,,75.0%
PHASE4-006,Phase 4: Launch,Testing & Bug Fixes,Security Configuration,Jon,Not Started,High,4.5,4.5,PHASE4-005,01-15-26,01-15-26,Configure WordFence Premium security settings,Security configured,,70.0%
PHASE4-007,Phase 4: Launch,Testing & Bug Fixes,Backup Configuration,Jon,Not Started,Medium,3,3,PHASE4-006,01-16-26,01-16-26,Set up automated backup procedures,Backup procedures configured,,85.0%
PHASE4-008,Phase 4: Launch,Testing & Bug Fixes,Final Testing & Validation,Jon,Not Started,Critical,9,9,PHASE4-007,01-17-26,01-20-26,Execute comprehensive pre-launch testing,Pre-launch testing complete,,40.0%
PHASE4-009,Phase 4: Launch,Testing & Bug Fixes,Launch Preparation,Jon,Not Started,Critical,6,6,PHASE4-008,01-21-26,01-21-26,Prepare launch checklist and rollback procedures,Launch preparation complete,,30.0%
PHASE4-010,Phase 4: Launch,Deployment,Staging Deployment,Ashley,Not Started,High,6,0,PHASE4-009,01-22-26,01-22-26,Deploy complete solution to staging,Staging deployment completed,,80.0%
PHASE4-011,Phase 4: Launch,Deployment,Staging Validation,Jon,Not Started,High,6,6,PHASE4-010,01-23-26,01-23-26,Comprehensive testing on staging,Staging validation completed,,30.0%
PHASE4-012,Phase 4: Launch,Deployment,Production Deployment,Ashley,Not Started,Critical,6,0,PHASE4-011,01-24-26,01-24-26,Execute production deployment,Production deployment completed,,75.0%
PHASE4-013,Phase 4: Launch,Deployment,DNS Cutover,Ashley,Not Started,Critical,4,0,PHASE4-012,01-27-26,01-27-26,Switch DNS to new environment - GO LIVE,DNS cutover completed,,90.0%
PHASE4-014,Phase 4: Launch,Post-Launch,Post-Deployment Verification,Both,Not Started,Critical,6,3,PHASE4-013,01-27-26,01-28-26,Verify production functionality,Post-deployment verification completed,,25.0%
PHASE4-015,Phase 4: Launch,Staff Training & Documentation,Staff Training,Jon,Not Started,High,3.75,3.75,PHASE4-014,01-29-26,01-29-26,Train staff on WordPress administration,Staff training completed,Staff Training & Documentation: 1 day = 7.5 hrs,10.0%
PHASE4-016,Phase 4: Launch,Staff Training & Documentation,User Documentation,Jon,Not Started,Medium,3.75,3.75,PHASE4-015,01-30-26,01-30-26,Create comprehensive user guides,User documentation completed,,30.0%
PHASE4-017,Phase 4: Launch,Support,Go-Live Support,Both,Not Started,High,12,6,PHASE4-013,01-27-26,02-03-26,Monitor and support during initial go-live period,Go-live support provided,,15.0%
PHASE4-018,Phase 4: Launch,Project Closure,Project Review,Both,Not Started,Medium,3,1.5,PHASE4-016,02-03-26,02-03-26,Review project outcomes and lessons learned,Project review completed,,20.0%
PHASE4-019,Phase 4: Launch,Project Closure,Final Sign-off,Client,Not Started,Critical,1.5,0,PHASE4-018,02-03-26,02-03-26,Get formal project completion approval,Final sign-off obtained,,0.0%

